{"defaultModel": "gpt-4-turbo-preview", "autoFix": false, "contextSize": 2000000, "autonomyLevel": "guided", "logLevel": "info", "workingDirectory": ".", "excludePatterns": ["node_modules/**", "dist/**", "build/**", ".git/**", "coverage/**", "*.log", "*.tmp", "*.temp", ".kritrima-sandbox/**"], "includePatterns": ["**/*.ts", "**/*.js", "**/*.tsx", "**/*.jsx", "**/*.py", "**/*.java", "**/*.cpp", "**/*.c", "**/*.cs", "**/*.go", "**/*.rs", "**/*.php", "**/*.rb", "**/*.swift", "**/*.kt", "**/*.scala", "**/*.json", "**/*.yaml", "**/*.yml", "**/*.xml", "**/*.md", "**/*.txt", "**/*.sql", "**/*.sh", "**/*.dock<PERSON><PERSON>le", "**/Dockerfile", "**/*.env"], "tools": {"shell": {"enabled": true, "timeout": 30000, "dangerousCommandsBlocked": true}, "file": {"enabled": true, "backupOnWrite": true, "maxFileSize": 1048576}, "edit": {"enabled": true, "createBackup": true}, "write": {"enabled": true, "createDirs": true}, "grep": {"enabled": true, "maxResults": 100}, "web": {"enabled": true, "timeout": 30000, "followRedirects": true}}, "models": {"openai": {"temperature": 0.7, "maxTokens": 4096, "topP": 1, "frequencyPenalty": 0, "presencePenalty": 0}, "anthropic": {"temperature": 0.7, "maxTokens": 4096}, "deepseek": {"temperature": 0.7, "maxTokens": 4096}, "ollama": {"temperature": 0.7, "numPredict": 4096}}}