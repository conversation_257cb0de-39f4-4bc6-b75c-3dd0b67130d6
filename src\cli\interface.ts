import * as readline from 'readline';
import chalk from 'chalk';
import ora, { Ora } from 'ora';
import { EventEmitter } from 'events';
import { Message, ChatSession, ExecutionPlan, CLIConfig } from '../types';
import { ContextManager } from '../context/manager';
import { ModelManager } from '../models/manager';
import { ToolManager } from '../tools/manager';
import { PlanExecutor } from '../core/executor';
import { ErrorDetector } from '../core/error-detector';
import { DiffReviewer } from '../core/diff-reviewer';

export class CLIInterface extends EventEmitter {
  private rl: readline.Interface;
  private contextManager: ContextManager;
  private modelManager: ModelManager;
  private toolManager: ToolManager;
  private planExecutor: PlanExecutor;
  private errorDetector: ErrorDetector;
  private diffReviewer: DiffReviewer;
  private currentSession: ChatSession | null = null;
  private config: CLIConfig;
  private spinner: any;

  constructor(config: CLIConfig) {
    super();
    this.config = config;
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout,
      prompt: chalk.cyan('kritrima> ')
    });

    this.contextManager = new ContextManager(config.contextSize);
    this.modelManager = new ModelManager();
    this.toolManager = new ToolManager();
    this.planExecutor = new PlanExecutor(this.toolManager);
    this.errorDetector = new ErrorDetector(config.workingDirectory);
    this.diffReviewer = new DiffReviewer();
    this.spinner = ora();

    this.setupEventHandlers();
    this.initializeSession();
  }

  private setupEventHandlers(): void {
    this.rl.on('line', this.handleUserInput.bind(this));
    this.rl.on('close', this.handleExit.bind(this));

    this.planExecutor.on('stepStart', this.handleStepStart.bind(this));
    this.planExecutor.on('stepComplete', this.handleStepComplete.bind(this));
    this.planExecutor.on('planComplete', this.handlePlanComplete.bind(this));

    this.errorDetector.on('errorDetected', this.handleErrorDetected.bind(this));
  }

  private async initializeSession(): Promise<void> {
    try {
      this.currentSession = {
        id: this.generateSessionId(),
        messages: [],
        context: [],
        model: await this.modelManager.getDefaultModel(),
        config: await this.modelManager.getDefaultConfig(),
        createdAt: new Date(),
        updatedAt: new Date()
      };
    } catch (error) {
      // No models available - create a minimal session
      console.log(chalk.yellow('⚠ No AI models available. Some features will be limited.'));
      console.log(chalk.blue('To enable AI features:'));
      console.log(chalk.blue('  1. Add API keys to .env file, OR'));
      console.log(chalk.blue('  2. Install and start Ollama (https://ollama.ai/)'));

      this.currentSession = {
        id: this.generateSessionId(),
        messages: [],
        context: [],
        model: { name: 'none', provider: 'none', maxTokens: 0, supportsStreaming: false, supportsFunctionCalling: false } as any,
        config: { model: 'none', temperature: 0.7, maxTokens: 4096 } as any,
        createdAt: new Date(),
        updatedAt: new Date()
      };
    }

    // Load initial context from working directory
    await this.contextManager.loadWorkingDirectory(this.config.workingDirectory);
    this.currentSession.context = this.contextManager.getContext();
  }

  public start(): void {
    this.displayWelcome();
    this.displayStatus();
    this.rl.prompt();
  }

  private displayWelcome(): void {
    console.log(chalk.bold.blue('\n🤖 Kritrima AI - Advanced Local CLI Terminal LLM Environment'));
    console.log(chalk.gray('Type "help" for commands, "exit" to quit\n'));
  }

  private displayStatus(): void {
    const contextFiles = this.currentSession?.context.length || 0;
    const totalTokens = this.contextManager.getTotalTokens();
    const model = this.currentSession?.model.name || 'none';

    console.log(chalk.dim(`Context: ${contextFiles} files (${totalTokens} tokens) | Model: ${model}`));
  }

  private async handleUserInput(input: string): Promise<void> {
    const trimmedInput = input.trim();

    if (!trimmedInput) {
      this.rl.prompt();
      return;
    }

    // Handle special commands
    if (await this.handleCommand(trimmedInput)) {
      this.rl.prompt();
      return;
    }

    // Process as AI query
    await this.processAIQuery(trimmedInput);
    this.rl.prompt();
  }

  private async handleCommand(input: string): Promise<boolean> {
    const [command, ...args] = input.split(' ');

    switch (command.toLowerCase()) {
      case 'help':
        this.displayHelp();
        return true;

      case 'status':
        this.displayStatus();
        return true;

      case 'models':
        await this.displayModels();
        return true;

      case 'model':
        if (args.length > 0) {
          await this.switchModel(args[0]);
        } else {
          console.log(chalk.yellow('Usage: model <model-name>'));
        }
        return true;

      case 'context':
        await this.handleContextCommand(args);
        return true;

      case 'scan':
        await this.scanForErrors();
        return true;

      case 'clear':
        this.clearSession();
        return true;

      case 'exit':
      case 'quit':
        this.handleExit();
        return true;

      default:
        return false;
    }
  }

  private displayHelp(): void {
    console.log(chalk.bold('\nAvailable Commands:'));
    console.log(chalk.cyan('  help') + '                 - Show this help message');
    console.log(chalk.cyan('  status') + '               - Show current status');
    console.log(chalk.cyan('  models') + '               - List available models');
    console.log(chalk.cyan('  model <name>') + '         - Switch to a different model');
    console.log(chalk.cyan('  context add <path>') + '   - Add file/directory to context');
    console.log(chalk.cyan('  context remove <path>') + ' - Remove file from context');
    console.log(chalk.cyan('  context list') + '         - List files in context');
    console.log(chalk.cyan('  scan') + '                 - Scan for errors in working directory');
    console.log(chalk.cyan('  clear') + '                - Clear current session');
    console.log(chalk.cyan('  exit/quit') + '            - Exit the application\n');
  }

  private async processAIQuery(query: string): Promise<void> {
    if (!this.currentSession) {
      console.log(chalk.red('No active session. Please restart the application.'));
      return;
    }

    // Check if AI models are available
    if (this.currentSession.model.name === 'none') {
      console.log(chalk.yellow('⚠ AI features are not available.'));
      console.log(chalk.blue('To enable AI features:'));
      console.log(chalk.blue('  1. Add API keys to .env file, OR'));
      console.log(chalk.blue('  2. Install and start Ollama (https://ollama.ai/)'));
      console.log(chalk.gray('\nYou can still use commands like: scan, context, help'));
      return;
    }

    try {
      this.spinner.start('Processing query...');

      // Add user message to session
      const userMessage: Message = {
        role: 'user',
        content: query
      };
      this.currentSession.messages.push(userMessage);

      // Get AI response with function calling
      const response = await this.modelManager.generateResponse(
        this.currentSession.messages,
        this.currentSession.context,
        this.toolManager.getAvailableTools(),
        this.currentSession.config
      );

      this.spinner.stop();

      // Handle streaming response
      if (response.toolCalls && response.toolCalls.length > 0) {
        await this.handleToolCalls(response.toolCalls);
      } else {
        console.log(chalk.green('\n' + response.content));
      }

      // Add assistant message to session
      const assistantMessage: Message = {
        role: 'assistant',
        content: response.content,
        tool_calls: response.toolCalls
      };
      this.currentSession.messages.push(assistantMessage);
      this.currentSession.updatedAt = new Date();

    } catch (error) {
      this.spinner.stop();
      console.log(chalk.red(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`));
    }
  }

  private async handleToolCalls(toolCalls: any[]): Promise<void> {
    console.log(chalk.yellow('\n🔧 Executing tools autonomously...\n'));

    for (const toolCall of toolCalls) {
      try {
        const tool = this.toolManager.getTool(toolCall.function.name);
        if (!tool) {
          console.log(chalk.red(`Unknown tool: ${toolCall.function.name}`));
          continue;
        }

        console.log(chalk.blue(`▶ ${tool.name}: ${tool.description}`));

        const args = JSON.parse(toolCall.function.arguments);
        const result = await tool.execute(args);

        if (result.success) {
          console.log(chalk.green(`✓ ${result.output}`));
        } else {
          console.log(chalk.red(`✗ ${result.error || 'Tool execution failed'}`));
        }

        // Add tool result to session
        const toolMessage: Message = {
          role: 'function',
          name: toolCall.function.name,
          content: JSON.stringify(result)
        };
        this.currentSession?.messages.push(toolMessage);

      } catch (error) {
        console.log(chalk.red(`Error executing ${toolCall.function.name}: ${error}`));
      }
    }
  }

  private generateSessionId(): string {
    return `session_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  }

  private handleExit(): void {
    console.log(chalk.blue('\nGoodbye! 👋'));
    process.exit(0);
  }

  private async displayModels(): Promise<void> {
    const models = await this.modelManager.getAvailableModels();
    console.log(chalk.bold('\nAvailable Models:'));

    for (const model of models) {
      const current = model.name === this.currentSession?.model.name ? chalk.green('(current)') : '';
      console.log(`  ${chalk.cyan(model.name)} - ${model.provider} ${current}`);
    }
    console.log();
  }

  private async switchModel(modelName: string): Promise<void> {
    try {
      const model = await this.modelManager.getModel(modelName);
      if (this.currentSession) {
        this.currentSession.model = model;
        this.currentSession.config = await this.modelManager.getConfigForModel(modelName);
        console.log(chalk.green(`Switched to model: ${modelName}`));
      }
    } catch (error) {
      console.log(chalk.red(`Failed to switch model: ${error}`));
    }
  }

  private async handleContextCommand(args: string[]): Promise<void> {
    if (args.length === 0) {
      console.log(chalk.yellow('Usage: context <add|remove|list> [path]'));
      return;
    }

    const [action, path] = args;

    switch (action.toLowerCase()) {
      case 'add':
        if (path) {
          await this.addToContext(path);
        } else {
          console.log(chalk.yellow('Usage: context add <path>'));
        }
        break;

      case 'remove':
        if (path) {
          this.removeFromContext(path);
        } else {
          console.log(chalk.yellow('Usage: context remove <path>'));
        }
        break;

      case 'list':
        this.listContext();
        break;

      default:
        console.log(chalk.yellow('Unknown context action. Use: add, remove, or list'));
    }
  }

  private async addToContext(path: string): Promise<void> {
    try {
      await this.contextManager.addFile(path);
      if (this.currentSession) {
        this.currentSession.context = this.contextManager.getContext();
      }
      console.log(chalk.green(`Added to context: ${path}`));
    } catch (error) {
      console.log(chalk.red(`Failed to add to context: ${error}`));
    }
  }

  private removeFromContext(path: string): void {
    this.contextManager.removeFile(path);
    if (this.currentSession) {
      this.currentSession.context = this.contextManager.getContext();
    }
    console.log(chalk.green(`Removed from context: ${path}`));
  }

  private listContext(): void {
    const context = this.contextManager.getContext();
    console.log(chalk.bold('\nContext Files:'));

    if (context.length === 0) {
      console.log(chalk.dim('  No files in context'));
    } else {
      for (const file of context) {
        console.log(`  ${chalk.cyan(file.path)} (${file.size} bytes)`);
      }
    }
    console.log();
  }

  private async scanForErrors(): Promise<void> {
    this.spinner.start('Scanning for errors...');

    try {
      const errors = await this.errorDetector.scanDirectory(this.config.workingDirectory);
      this.spinner.stop();

      if (errors.length === 0) {
        console.log(chalk.green('✓ No errors detected'));
      } else {
        console.log(chalk.yellow(`Found ${errors.length} issues:`));
        for (const error of errors) {
          const severity = error.severity === 'error' ? chalk.red('ERROR') :
                          error.severity === 'warning' ? chalk.yellow('WARN') :
                          chalk.blue('INFO');
          console.log(`  ${severity} ${error.file}:${error.line} - ${error.message}`);
        }

        if (this.config.autoFix) {
          console.log(chalk.blue('\nAttempting automatic fixes...'));
          await this.autoFixErrors(errors);
        }
      }
    } catch (error) {
      this.spinner.stop();
      console.log(chalk.red(`Error scanning: ${error}`));
    }
  }

  private async autoFixErrors(errors: any[]): Promise<void> {
    const fixableErrors = errors.filter(e => e.fixable);

    if (fixableErrors.length === 0) {
      console.log(chalk.yellow('No automatically fixable errors found'));
      return;
    }

    for (const error of fixableErrors) {
      try {
        await this.errorDetector.fixError(error);
        console.log(chalk.green(`✓ Fixed: ${error.message}`));
      } catch (fixError) {
        console.log(chalk.red(`✗ Failed to fix: ${error.message}`));
      }
    }
  }

  private clearSession(): void {
    if (this.currentSession) {
      this.currentSession.messages = [];
      console.log(chalk.green('Session cleared'));
    }
  }

  private handleStepStart(step: any): void {
    console.log(chalk.blue(`▶ ${step.description}`));
  }

  private handleStepComplete(step: any): void {
    if (step.result?.success) {
      console.log(chalk.green(`✓ ${step.description}`));
    } else {
      console.log(chalk.red(`✗ ${step.description}: ${step.result?.error}`));
    }
  }

  private handlePlanComplete(plan: ExecutionPlan): void {
    const completed = plan.steps.filter(s => s.status === 'completed').length;
    const total = plan.steps.length;
    console.log(chalk.green(`\n🎉 Plan completed: ${completed}/${total} steps successful`));
  }

  private handleErrorDetected(error: any): void {
    console.log(chalk.yellow(`\n⚠ Error detected: ${error.file}:${error.line} - ${error.message}`));
  }
}
