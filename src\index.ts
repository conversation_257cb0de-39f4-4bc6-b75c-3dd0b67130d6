#!/usr/bin/env node

import * as dotenv from 'dotenv';
import { Command } from 'commander';
import chalk from 'chalk';
import { CLIInterface } from './cli/interface';
import { CLIConfig } from './types';

// Load environment variables
dotenv.config();

const program = new Command();

program
  .name('kritrima')
  .description('Kritrima AI - Advanced Local CLI Terminal LLM Interaction Environment')
  .version('1.0.0');

program
  .option('-m, --model <model>', 'AI model to use', 'gpt-4-turbo-preview')
  .option('-d, --directory <path>', 'Working directory', process.cwd())
  .option('-c, --context-size <size>', 'Context size in tokens', '2000000')
  .option('-a, --autonomy <level>', 'Autonomy level (full|guided|manual)', 'guided')
  .option('--auto-fix', 'Enable automatic error fixing', false)
  .option('--log-level <level>', 'Log level (debug|info|warn|error)', 'info')
  .option('--config <path>', 'Configuration file path')
  .action(async (options) => {
    try {
      // Load configuration
      const config = await loadConfig(options);

      // Validate environment
      await validateEnvironment();

      // Create and start CLI interface
      const cli = new CLIInterface(config);

      // Handle graceful shutdown
      process.on('SIGINT', () => {
        console.log(chalk.blue('\n\nShutting down gracefully...'));
        process.exit(0);
      });

      process.on('SIGTERM', () => {
        console.log(chalk.blue('\n\nShutting down gracefully...'));
        process.exit(0);
      });

      // Start the CLI
      cli.start();

    } catch (error) {
      console.error(chalk.red(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`));
      process.exit(1);
    }
  });

// Add subcommands
program
  .command('init')
  .description('Initialize Kritrima AI in current directory')
  .option('-f, --force', 'Force initialization even if already initialized')
  .action(async (options) => {
    await initializeProject(options);
  });

program
  .command('scan')
  .description('Scan directory for errors and issues')
  .option('-d, --directory <path>', 'Directory to scan', process.cwd())
  .option('--fix', 'Automatically fix issues where possible')
  .action(async (options) => {
    await scanDirectory(options);
  });

program
  .command('models')
  .description('List available AI models')
  .action(async () => {
    await listModels();
  });

program
  .command('config')
  .description('Show current configuration')
  .action(async () => {
    await showConfig();
  });

async function loadConfig(options: any): Promise<CLIConfig> {
  let config: CLIConfig = {
    defaultModel: options.model,
    autoFix: options.autoFix,
    contextSize: parseInt(options.contextSize),
    autonomyLevel: options.autonomy,
    logLevel: options.logLevel,
    workingDirectory: options.directory
  };

  // Load from config file if specified
  if (options.config) {
    try {
      const fs = require('fs');
      const configFile = JSON.parse(fs.readFileSync(options.config, 'utf8'));
      config = { ...config, ...configFile };
    } catch (error) {
      console.warn(chalk.yellow(`Warning: Could not load config file: ${error}`));
    }
  }

  return config;
}

async function validateEnvironment(): Promise<void> {
  const requiredEnvVars = [];
  const warnings = [];

  // Check for API keys
  if (!process.env.OPENAI_API_KEY) {
    warnings.push('OPENAI_API_KEY not set - OpenAI models will not be available');
  }

  if (!process.env.ANTHROPIC_API_KEY) {
    warnings.push('ANTHROPIC_API_KEY not set - Anthropic models will not be available');
  }

  if (!process.env.DEEPSEEK_API_KEY) {
    warnings.push('DEEPSEEK_API_KEY not set - Deepseek models will not be available');
  }

  // Check Node.js version
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);

  if (majorVersion < 18) {
    throw new Error(`Node.js 18 or higher is required. Current version: ${nodeVersion}`);
  }

  // Show warnings
  if (warnings.length > 0) {
    console.log(chalk.yellow('\nWarnings:'));
    warnings.forEach(warning => console.log(chalk.yellow(`  • ${warning}`)));
    console.log();
  }

  // Check if at least one provider is available
  if (!process.env.OPENAI_API_KEY &&
      !process.env.ANTHROPIC_API_KEY &&
      !process.env.DEEPSEEK_API_KEY) {

    // Check if Ollama is running
    try {
      const axios = require('axios');
      await axios.get('http://localhost:11434/api/tags', { timeout: 5000 });
      console.log(chalk.green('✓ Ollama detected - local models available'));
    } catch (error) {
      throw new Error('No AI providers available. Please set API keys or start Ollama.');
    }
  }
}

async function initializeProject(options: any): Promise<void> {
  const fs = require('fs');
  const path = require('path');

  const configPath = path.join(process.cwd(), '.kritrima.json');

  if (fs.existsSync(configPath) && !options.force) {
    console.log(chalk.yellow('Kritrima AI already initialized. Use --force to reinitialize.'));
    return;
  }

  const defaultConfig = {
    defaultModel: 'gpt-4-turbo-preview',
    autoFix: false,
    contextSize: 2000000,
    autonomyLevel: 'guided',
    logLevel: 'info',
    workingDirectory: process.cwd(),
    excludePatterns: [
      'node_modules/**',
      'dist/**',
      'build/**',
      '.git/**',
      'coverage/**'
    ]
  };

  fs.writeFileSync(configPath, JSON.stringify(defaultConfig, null, 2));

  // Create .env template
  const envPath = path.join(process.cwd(), '.env.example');
  const envTemplate = `# Kritrima AI Configuration
# Copy this file to .env and fill in your API keys

# OpenAI API Key
OPENAI_API_KEY=your_openai_api_key_here

# Anthropic API Key
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Deepseek API Key
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# Ollama Base URL (for local models)
OLLAMA_BASE_URL=http://localhost:11434
`;

  fs.writeFileSync(envPath, envTemplate);

  console.log(chalk.green('✓ Kritrima AI initialized successfully!'));
  console.log(chalk.blue('  • Configuration saved to .kritrima.json'));
  console.log(chalk.blue('  • Environment template saved to .env.example'));
  console.log(chalk.yellow('\nNext steps:'));
  console.log(chalk.yellow('  1. Copy .env.example to .env'));
  console.log(chalk.yellow('  2. Add your API keys to .env'));
  console.log(chalk.yellow('  3. Run "kritrima" to start'));
}

async function scanDirectory(options: any): Promise<void> {
  const { ErrorDetector } = require('./core/error-detector');

  console.log(chalk.blue(`Scanning ${options.directory}...`));

  const detector = new ErrorDetector(options.directory);
  const errors = await detector.scanDirectory(options.directory);

  if (errors.length === 0) {
    console.log(chalk.green('✓ No issues found'));
    return;
  }

  console.log(chalk.yellow(`Found ${errors.length} issues:`));

  for (const error of errors) {
    const severity = error.severity === 'error' ? chalk.red('ERROR') :
                    error.severity === 'warning' ? chalk.yellow('WARN') :
                    chalk.blue('INFO');

    console.log(`  ${severity} ${error.file}:${error.line} - ${error.message}`);
  }

  if (options.fix) {
    const fixableErrors = errors.filter((e: any) => e.fixable);
    console.log(chalk.blue(`\nAttempting to fix ${fixableErrors.length} issues...`));

    for (const error of fixableErrors) {
      try {
        await detector.fixError(error);
        console.log(chalk.green(`✓ Fixed: ${error.message}`));
      } catch (fixError) {
        console.log(chalk.red(`✗ Failed to fix: ${error.message}`));
      }
    }
  }
}

async function listModels(): Promise<void> {
  const { ModelManager } = require('./models/manager');

  const manager = new ModelManager();
  const models = await manager.getAvailableModels();

  console.log(chalk.bold('\nAvailable Models:'));

  for (const model of models) {
    const status = model.provider === 'ollama' ? '(local)' : '(cloud)';
    console.log(`  ${chalk.cyan(model.name)} - ${model.provider} ${status}`);
    console.log(`    Max tokens: ${model.maxTokens.toLocaleString()}`);
    console.log(`    Streaming: ${model.supportsStreaming ? '✓' : '✗'}`);
    console.log(`    Function calling: ${model.supportsFunctionCalling ? '✓' : '✗'}`);
    console.log();
  }
}

async function showConfig(): Promise<void> {
  const fs = require('fs');
  const path = require('path');

  const configPath = path.join(process.cwd(), '.kritrima.json');

  if (!fs.existsSync(configPath)) {
    console.log(chalk.yellow('No configuration file found. Run "kritrima init" to create one.'));
    return;
  }

  const config = JSON.parse(fs.readFileSync(configPath, 'utf8'));

  console.log(chalk.bold('\nCurrent Configuration:'));
  console.log(JSON.stringify(config, null, 2));
}

// Parse command line arguments
program.parse();
