# Kritrima AI - Quick Start Guide

Get up and running with Kritrima AI in minutes!

## 🚀 Quick Installation

### 1. Prerequisites
- Node.js 18+ installed
- npm or yarn package manager

### 2. Install Dependencies
```bash
npm install
```

### 3. Build the Project
```bash
npm run build
```

### 4. Test Installation
```bash
node test-installation.js
```

## 🔧 Configuration

### 1. Initialize Configuration
```bash
node dist/index.js init
```

This creates:
- `.kritrima.json` - Main configuration file
- `.env.example` - Environment variables template

### 2. Set Up API Keys (Optional)
Copy `.env.example` to `.env` and add your API keys:

```bash
cp .env.example .env
```

Edit `.env` file:
```env
# Add your API keys (at least one is required)
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_anthropic_key_here
DEEPSEEK_API_KEY=your_deepseek_key_here

# For local models (Ollama)
OLLAMA_BASE_URL=http://localhost:11434
```

### 3. Install Ollama (For Local Models)
If you want to use local models without API keys:

1. Download Ollama from https://ollama.ai/
2. Install and start Ollama
3. Pull a model: `ollama pull llama2`

## 🎯 Basic Usage

### 1. Start Interactive Mode
```bash
node dist/index.js
```

### 2. Available Commands in Interactive Mode
- `help` - Show available commands
- `status` - Show current status and context
- `models` - List available AI models
- `model <name>` - Switch to a different model
- `context add <path>` - Add file/directory to context
- `scan` - Scan for errors in working directory
- `clear` - Clear current session
- `exit` - Exit the application

### 3. CLI Commands
```bash
# Scan for errors
node dist/index.js scan --directory ./src

# Scan and auto-fix
node dist/index.js scan --directory ./src --fix

# List available models
node dist/index.js models

# Show configuration
node dist/index.js config
```

## 💡 Example Interactions

### Code Analysis
```
kritrima> Analyze my TypeScript project and fix any errors

🔧 Executing tools autonomously...
▶ shell: Scanning TypeScript files for errors
✓ Found 3 syntax errors and 5 warnings
▶ edit: Fixing missing semicolons in src/utils.ts
✓ Fixed 2 missing semicolons
```

### File Operations
```
kritrima> Create a new React component called UserProfile

🔧 Executing tools autonomously...
▶ write: Creating UserProfile component file
✓ Created src/components/UserProfile.tsx
▶ edit: Adding export to index.ts
✓ Updated component exports
```

### Search Operations
```
kritrima> Find all TODO comments in the codebase

🔧 Executing tools autonomously...
▶ grep: Searching for TODO comments
✓ Found 12 TODO comments across 8 files
▶ write: Creating TODO summary report
✓ Created TODO_SUMMARY.md
```

## 🛠️ Available Tools

Kritrima AI comes with 6 powerful autonomous tools:

1. **Shell Tool** - Execute bash/cmd commands safely
2. **File Tool** - Read, write, and manage files
3. **Edit Tool** - Precise line-based file editing
4. **Write Tool** - Create new files with various modes
5. **Grep Tool** - Advanced pattern searching
6. **Web Tool** - Fetch web content and make HTTP requests

## 🔍 Error Detection Features

Kritrima AI automatically detects:

- **Syntax Errors**: Missing semicolons, brackets, colons
- **Code Quality**: Long lines, trailing whitespace, unused variables
- **Security Issues**: Hardcoded secrets, SQL injection risks
- **Best Practices**: TODO comments, console.log statements
- **Language-Specific**: TypeScript types, Python indentation

## 📊 Testing the Installation

Run the comprehensive test:
```bash
node test-installation.js
```

This will verify:
- ✅ Build output exists
- ✅ All source files present
- ✅ Configuration files created
- ✅ Dependencies installed
- ✅ CLI commands working

## 🎨 Customization

### Model Configuration
Edit `.kritrima.json` to customize:
```json
{
  "defaultModel": "gpt-4-turbo-preview",
  "autonomyLevel": "guided",
  "autoFix": false,
  "contextSize": 2000000
}
```

### Tool Configuration
Enable/disable specific tools:
```json
{
  "tools": {
    "shell": { "enabled": true, "timeout": 30000 },
    "file": { "enabled": true, "backupOnWrite": true },
    "web": { "enabled": false }
  }
}
```

## 🚨 Troubleshooting

### Common Issues

1. **"No AI providers available"**
   - Add API keys to `.env` file, OR
   - Install and start Ollama for local models

2. **"Command not found"**
   - Make sure you built the project: `npm run build`
   - Use full path: `node dist/index.js`

3. **"Permission denied"**
   - Check file permissions
   - Run with appropriate privileges

4. **"Module not found"**
   - Install dependencies: `npm install`
   - Check Node.js version (18+ required)

### Getting Help

1. Use `help` command in interactive mode
2. Check the full README.md for detailed documentation
3. Run `node dist/index.js --help` for CLI options

## 🎉 You're Ready!

Kritrima AI is now installed and ready to use. Start with:

```bash
node dist/index.js
```

Type `help` to see all available commands and start exploring the powerful autonomous AI coding assistant!

---

**Next Steps:**
- Explore the examples/ directory for sample code with intentional errors
- Try the autonomous error detection and fixing features
- Experiment with different AI models and autonomy levels
- Check out the advanced features in the full documentation
