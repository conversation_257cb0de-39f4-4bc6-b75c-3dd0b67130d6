// Sample JavaScript file with intentional errors for testing Kritrima AI

// Missing semicolon
const message = "Hello World"

// Console.log statement (should be flagged)
console.log("Debug message")

// TODO comment
// TODO: Implement proper error handling

// Potential security issue
const password = "hardcoded_password_123"

// Long line that exceeds 120 characters
const veryLongVariableName = "This is a very long string that definitely exceeds the maximum line length limit and should be flagged by the linter"

// Trailing whitespace (invisible in this example)
const example = "test";   

// Undefined variable usage
function testFunction() {
    return undefinedVariable + 5;
}

// Missing function parameter types (for TypeScript)
function calculate(a, b) {
    return a + b
}

// Unused variable
const unusedVariable = "This variable is never used"

// Mixed indentation (tabs and spaces)
function mixedIndentation() {
	  if (true) {
        return "mixed tabs and spaces"
    }
}

// FIXME comment
// FIXME: This function needs optimization

// Potential SQL injection
function getUserData(userId) {
    const query = "SELECT * FROM users WHERE id = " + userId;
    return query;
}

// Missing error handling
function riskyOperation() {
    JSON.parse(invalidJson);
}

// Hardcoded API key
const apiKey = "sk-1234567890abcdef"

// Empty catch block
try {
    riskyOperation();
} catch (error) {
    // Empty catch block
}

// Magic numbers
function calculateDiscount(price) {
    return price * 0.15; // What does 0.15 represent?
}

// Inconsistent naming
const user_name = "john_doe";
const userAge = 25;
const UserEmail = "<EMAIL>";

// Dead code
function neverCalled() {
    return "This function is never called";
}

// Complex nested conditions
function complexLogic(user) {
    if (user) {
        if (user.isActive) {
            if (user.permissions) {
                if (user.permissions.canRead) {
                    if (user.permissions.canWrite) {
                        return true;
                    }
                }
            }
        }
    }
    return false;
}
